-- <PERSON><PERSON> Script to create ResolutionVotes table
-- Based on ResolutionVote entity definition and Entity Framework migration
-- This table stores votes cast by board members on resolutions or resolution items

-- Create ResolutionVotes table
CREATE TABLE [dbo].[ResolutionVotes] (
    -- Primary Key
    [Id] int IDENTITY(1,1) NOT NULL,
    
    -- Core voting fields
    [ResolutionId] int NOT NULL,
    [ResolutionItemId] int NULL,
    [BoardMemberId] int NOT NULL,
    [VoteValue] int NOT NULL, -- 1=Approve, 2=Reject, 3=Abstain
    [Comments] nvarchar(max) NULL,
    [IsActive] bit NOT NULL DEFAULT 1,
    
    -- Audit fields (inherited from FullAuditedEntity)
    [CreatedAt] datetime2 NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] int NOT NULL,
    [UpdatedAt] datetime2 NULL DEFAULT GETUTCDATE(),
    [UpdatedBy] int NULL,
    [DeletedAt] datetime2 NULL,
    [IsDeleted] bit NULL DEFAULT 0,
    [DeletedBy] int NULL,
    
    -- Primary Key Constraint
    CONSTRAINT [PK_ResolutionVotes] PRIMARY KEY ([Id]),
    
    -- Foreign Key Constraints
    CONSTRAINT [FK_ResolutionVotes_Resolutions_ResolutionId] 
        FOREIGN KEY ([ResolutionId]) 
        REFERENCES [dbo].[Resolutions] ([Id]) 
        ON DELETE NO ACTION,
        
    CONSTRAINT [FK_ResolutionVotes_ResolutionItems_ResolutionItemId] 
        FOREIGN KEY ([ResolutionItemId]) 
        REFERENCES [dbo].[ResolutionItems] ([Id]) 
        ON DELETE NO ACTION,
        
    CONSTRAINT [FK_ResolutionVotes_BoardMembers_BoardMemberId] 
        FOREIGN KEY ([BoardMemberId]) 
        REFERENCES [dbo].[BoardMembers] ([Id]) 
        ON DELETE NO ACTION,
        
    CONSTRAINT [FK_ResolutionVotes_AspNetUsers_CreatedBy] 
        FOREIGN KEY ([CreatedBy]) 
        REFERENCES [dbo].[AspNetUsers] ([Id]) 
        ON DELETE NO ACTION,
        
    CONSTRAINT [FK_ResolutionVotes_AspNetUsers_UpdatedBy] 
        FOREIGN KEY ([UpdatedBy]) 
        REFERENCES [dbo].[AspNetUsers] ([Id]) 
        ON DELETE NO ACTION,
        
    CONSTRAINT [FK_ResolutionVotes_AspNetUsers_DeletedBy] 
        FOREIGN KEY ([DeletedBy]) 
        REFERENCES [dbo].[AspNetUsers] ([Id]) 
        ON DELETE NO ACTION,
        
    -- Check Constraints
    CONSTRAINT [CK_ResolutionVotes_VoteValue] 
        CHECK ([VoteValue] IN (1, 2, 3)), -- 1=Approve, 2=Reject, 3=Abstain
        
    CONSTRAINT [CK_ResolutionVotes_IsActive] 
        CHECK ([IsActive] IN (0, 1))
);

-- Create Indexes for performance optimization
CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_ResolutionId] 
    ON [dbo].[ResolutionVotes] ([ResolutionId]);

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_ResolutionItemId] 
    ON [dbo].[ResolutionVotes] ([ResolutionItemId]);

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_BoardMemberId] 
    ON [dbo].[ResolutionVotes] ([BoardMemberId]);

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_CreatedBy] 
    ON [dbo].[ResolutionVotes] ([CreatedBy]);

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_UpdatedBy] 
    ON [dbo].[ResolutionVotes] ([UpdatedBy]);

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_DeletedBy] 
    ON [dbo].[ResolutionVotes] ([DeletedBy]);

-- Composite indexes for common query patterns
CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_Resolution_BoardMember] 
    ON [dbo].[ResolutionVotes] ([ResolutionId], [BoardMemberId])
    WHERE [IsDeleted] = 0 OR [IsDeleted] IS NULL;

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_ResolutionItem_BoardMember] 
    ON [dbo].[ResolutionVotes] ([ResolutionItemId], [BoardMemberId])
    WHERE [IsDeleted] = 0 OR [IsDeleted] IS NULL;

CREATE NONCLUSTERED INDEX [IX_ResolutionVotes_Active_Votes] 
    ON [dbo].[ResolutionVotes] ([ResolutionId], [IsActive], [VoteValue])
    WHERE [IsDeleted] = 0 OR [IsDeleted] IS NULL;

-- Add table and column comments for documentation
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Stores votes cast by board members on resolutions or resolution items. Supports voting suspension and reactivation scenarios.',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Foreign key reference to Resolution entity',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'ResolutionId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Foreign key reference to ResolutionItem entity (NULL if voting on resolution as whole)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'ResolutionItemId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Foreign key reference to BoardMember entity',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'BoardMemberId';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Vote value: 1=Approve, 2=Reject, 3=Abstain',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'VoteValue';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Optional comments or notes about the vote',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'Comments';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Indicates if this vote is active (used for vote suspension scenarios)',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'ResolutionVotes',
    @level2type = N'COLUMN', @level2name = N'IsActive';

-- Verify table creation
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ResolutionVotes'
ORDER BY ORDINAL_POSITION;

PRINT 'ResolutionVotes table created successfully with all constraints and indexes.';
